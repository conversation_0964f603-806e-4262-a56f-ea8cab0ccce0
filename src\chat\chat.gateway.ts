import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import {
  Logger,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { ChatService } from './chat.service';
import { SendMessageDto } from './dto/send-message.dto';
import { WsAuthGuard } from './guards/ws-auth.guard';
import { WsThrottlerGuard } from './guards/ws-throttler.guard';

interface AuthenticatedSocket extends Socket {
  userId?: number;
  username?: string;
}

@WebSocketGateway({
  cors: {
    origin: [process.env.CLIENT_URL || 'http://localhost:5173', 'http://localhost:5173','http://**************:3000'],
    credentials: true,
  },
  namespace: '/chat',
})
@UsePipes(new ValidationPipe({ transform: true }))
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);
  private readonly connectedUsers = new Map<number, Set<string>>(); // userId -> Set of socketIds

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly chatService: ChatService,
    @Inject(forwardRef(() => WsThrottlerGuard))
    private readonly wsThrottlerGuard: WsThrottlerGuard,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = this.extractTokenFromSocket(client);
      if (!token) {
        this.logger.warn(`Connection rejected: No token provided`);
        client.disconnect();
        return;
      }

      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('JWT_SECRET'),
      });

      client.userId = payload.userId;
      client.username = payload.username;

      // Track connected user
      if (!this.connectedUsers.has(payload.userId)) {
        this.connectedUsers.set(payload.userId, new Set());
      }
      this.connectedUsers.get(payload.userId)!.add(client.id);

      this.logger.log(
        `User ${payload.username} (${payload.userId}) connected with socket ${client.id}`,
      );

      // Join user to their personal room for direct messaging
      client.join(`user_${payload.userId}`);

      // Auto-join user to all their chat rooms
      await this.autoJoinUserChats(client);

      // Emit connection success
      client.emit('connected', {
        message: 'Successfully connected to chat',
        userId: payload.userId,
      });
    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      // Remove from connected users
      const userSockets = this.connectedUsers.get(client.userId);
      if (userSockets) {
        userSockets.delete(client.id);
        if (userSockets.size === 0) {
          this.connectedUsers.delete(client.userId);
        }
      }

      // Clean up rate limiting data
      this.wsThrottlerGuard.cleanupUser(client.userId);

      this.logger.log(
        `User ${client.username} (${client.userId}) disconnected`,
      );
    }
  }

  @SubscribeMessage('join_chat')
  @UseGuards(WsAuthGuard)
  async handleJoinChat(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string },
  ) {
    try {
      if (!client.userId) {
        throw new Error('User not authenticated');
      }

      // Validate user has access to this chat
      await this.chatService.validateChatAccess(data.chatId, client.userId);

      // Join the chat room
      client.join(`chat_${data.chatId}`);

      this.logger.log(`User ${client.userId} joined chat ${data.chatId}`);

      client.emit('joined_chat', {
        chatId: data.chatId,
        message: 'Successfully joined chat',
      });
    } catch (error) {
      this.logger.error(`Join chat error: ${error.message}`);
      client.emit('error', {
        message: error.message,
        event: 'join_chat',
      });
    }
  }

  @SubscribeMessage('leave_chat')
  @UseGuards(WsAuthGuard)
  async handleLeaveChat(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string },
  ) {
    client.leave(`chat_${data.chatId}`);

    this.logger.log(`User ${client.userId} left chat ${data.chatId}`);

    client.emit('left_chat', {
      chatId: data.chatId,
      message: 'Successfully left chat',
    });
  }

  @SubscribeMessage('send_message')
  @UseGuards(WsAuthGuard, WsThrottlerGuard)
  async handleSendMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string; message: SendMessageDto },
  ) {
    try {
      if (!client.userId) {
        throw new Error('User not authenticated');
      }

      const message = await this.chatService.sendMessage(
        data.chatId,
        client.userId,
        data.message,
      );

      // Ensure sender is in the chat room
      client.join(`chat_${data.chatId}`);

      // Get chat participants to notify them directly if needed
      const chat = await this.chatService.validateChatAccess(
        data.chatId,
        client.userId,
      );

      const messageData = {
        chatId: data.chatId,
        message: {
          id: message.id,
          content: message.content,
          type: message.type,
          sender: {
            id: message.sender.id,
            username: message.sender.username,
            level: message.sender.level,
          },
          createdAt: message.createdAt,
          isRead: false, // New message is unread for receivers
        },
      };

      // Emit to all users in the chat room
      this.server.to(`chat_${data.chatId}`).emit('new_message', messageData);

      // Also notify participants directly (fallback for users not in room)
      for (const participant of chat.participants) {
        if (participant.id !== client.userId) {
          const unreadCount = await this.chatService.getUnreadMessageCount(
            chat.id,
            participant.id,
          );
          await this.notifyUser(participant.id, 'new_message', {
            ...messageData,
            unreadCount,
          });
        }
      }

      this.logger.log(
        `Message sent by user ${client.userId} in chat ${data.chatId}`,
      );
    } catch (error) {
      this.logger.error(`Send message error: ${error.message}`);
      client.emit('error', {
        message: error.message,
        event: 'send_message',
      });
    }
  }

  @SubscribeMessage('mark_read')
  @UseGuards(WsAuthGuard)
  async handleMarkRead(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string },
  ) {
    try {
      if (!client.userId) {
        throw new Error('User not authenticated');
      }

      const result = await this.chatService.markMessagesAsRead(
        data.chatId,
        client.userId,
      );

      // Emit to the chat room that messages were read
      this.server.to(`chat_${data.chatId}`).emit('messages_read', {
        chatId: data.chatId,
        userId: client.userId,
        markedCount: result.markedCount,
      });

      this.logger.log(
        `User ${client.userId} marked ${result.markedCount} messages as read in chat ${data.chatId}`,
      );
    } catch (error) {
      this.logger.error(`Mark read error: ${error.message}`);
      client.emit('error', {
        message: error.message,
        event: 'mark_read',
      });
    }
  }

  @SubscribeMessage('typing_start')
  @UseGuards(WsAuthGuard)
  async handleTypingStart(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string },
  ) {
    // Broadcast to other users in the chat (excluding sender)
    client.to(`chat_${data.chatId}`).emit('user_typing', {
      chatId: data.chatId,
      userId: client.userId,
      username: client.username,
      isTyping: true,
    });
  }

  @SubscribeMessage('typing_stop')
  @UseGuards(WsAuthGuard)
  async handleTypingStop(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string },
  ) {
    // Broadcast to other users in the chat (excluding sender)
    client.to(`chat_${data.chatId}`).emit('user_typing', {
      chatId: data.chatId,
      userId: client.userId,
      username: client.username,
      isTyping: false,
    });
  }

  @SubscribeMessage('ping')
  @UseGuards(WsAuthGuard)
  async handlePing(@ConnectedSocket() client: AuthenticatedSocket) {
    // Respond with pong to maintain connection health
    client.emit('pong', {
      timestamp: Date.now(),
      userId: client.userId,
    });
  }

  // Method to send notifications to specific users
  async notifyUser(userId: number, event: string, data: any) {
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets && userSockets.size > 0) {
      this.server.to(`user_${userId}`).emit(event, data);
    }
  }

  // Method to check if user is online
  isUserOnline(userId: number): boolean {
    return this.connectedUsers.has(userId);
  }

  private extractTokenFromSocket(socket: Socket): string | null {
    const token =
      socket.handshake.auth?.token ||
      socket.handshake.headers?.authorization?.split(' ')[1];
    return token || null;
  }

  // Auto-join user to all their chat rooms when they connect
  private async autoJoinUserChats(client: AuthenticatedSocket) {
    if (!client.userId) return;

    try {
      // Get user's chats (limit to recent ones to avoid performance issues)
      const result = await this.chatService.getUserChats(client.userId, {
        limit: 50,
      });

      // Join all chat rooms
      for (const chat of result.chats) {
        client.join(`chat_${chat.id}`);
      }

      this.logger.log(
        `User ${client.userId} auto-joined ${result.chats.length} chat rooms`,
      );
    } catch (error) {
      this.logger.error(
        `Error auto-joining chats for user ${client.userId}: ${error.message}`,
      );
    }
  }
}
