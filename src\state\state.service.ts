import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, Repository } from 'typeorm';
import { State, GovernmentType } from './entity/state.entity';
import { Region, RegionStatus } from '../region/entity/region.entity';
import { User } from '../user/entity/user.entity';
import { CreateStateDto } from './dto/create-state.dto';
import { UpdateStateDto } from './dto/update-state.dto';
import { AddRegionDto } from './dto/add-region.dto';
import { Party } from '../party/entity/party.entity';
import { RegionService } from '../region/region.service';
import { Inject, forwardRef } from '@nestjs/common';
import { StateElectionService } from '../state-election/state-election.service';

@Injectable()
export class StateService {
  constructor(
    @InjectRepository(State)
    private stateRepository: Repository<State>,
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Party)
    private partyRepository: Repository<Party>,
    private regionService: RegionService,
    @Inject(forwardRef(() => StateElectionService))
    private stateElectionService: StateElectionService,
  ) {}

  async findAll(): Promise<State[]> {
    const states = await this.stateRepository.find({
      relations: ['leader', 'regions'],
    });

    // Get the count of users in each region for all states using RegionService
    for (const state of states) {
      for (const region of state.regions) {
        // Use the RegionService to add population to each region
        await this.regionService.addPopulation(region);
      }
    }

    return states;
  }
  async findAllCount(): Promise<number> {
    return await this.stateRepository.count();
  }

  async findById(id: string): Promise<State> {
    const state = await this.stateRepository.findOne({
      where: { id },
      relations: ['leader', 'regions', 'regions.parties'],
    });

    if (!state) {
      throw new NotFoundException(`State with ID ${id} not found`);
    }

    // Get the count of users in each region using RegionService
    for (const region of state.regions) {
      await this.regionService.addPopulation(region);
    }

    return state;
  }

  async findByUserId(userId: number): Promise<State> {
    const region = await this.regionRepository.findOne({
      where: { users: { id: userId } },
      relations: ['state'],
    });

    if (!region?.state) {
      throw new NotFoundException('You are not part of any state');
    }

    const state = await this.stateRepository.findOne({
      where: { id: region.state.id },
      relations: ['regions', 'leader'],
    });

    if (!state) {
      throw new NotFoundException('State not found');
    }

    for (const r of state.regions) {
      await this.regionService.addPopulation(r);
    }

    return state;
  }

  async findStateLeader(userId: number): Promise<State | null> {
    const state = await this.stateRepository.findOne({
      where: { leader: { id: userId } },
      relations: ['regions', 'leader'],
    });

    if (!state) {
      return null;
    }

    for (const region of state.regions) {
      await this.regionService.addPopulation(region);
    }

    return state;
  }

  async create(userId: number, createStateDto: CreateStateDto): Promise<State> {
    // Get the user who will be the leader
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region', 'region.state', 'leadingParty'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.region.state) {
      throw new BadRequestException('Region is already part of a state');
    }

    // Check if the user is a party leader
    if (!user.leadingParty) {
      throw new BadRequestException(
        'User must be a party leader to create a state.',
      );
    }

    // Check if the user has enough gold
    if (user.gold < 500) {
      throw new BadRequestException('Not enough gold to create a state.');
    }

    // Check if there is at least one party in the region
    const partiesInRegion = await this.partyRepository.find({
      where: { region: { id: user.region.id } },
    });

    if (partiesInRegion.length === 0) {
      throw new BadRequestException(
        'There must be at least one party in the region to create a state.',
      );
    }

    // Check again if the user has enough gold (in case it changed since the first check)
    if (user.gold < 500) {
      throw new BadRequestException(
        'Not enough gold to create a state. You need at least 500 gold.',
      );
    }

    // Deduct gold for state creation
    user.gold -= 500;
    await this.userRepository.save(user); // Save the updated gold balance

    // Create the state
    const newState = this.stateRepository.create({
      name: createStateDto.name,
      description: createStateDto.description,
      flagUrl: createStateDto.flagUrl,
      leader: user,
      isActive: true,
      treasury: 0,
      resourceReserves: {},
      allies: [],
      enemies: [],
      governmentType: GovernmentType.REPUBLIC, // Default to dictatorship
    });

    // Save the state first to get an ID
    const savedState = await this.stateRepository.save(newState);

    // If the user's region should be part of the state
    if (createStateDto.includeLeaderRegion && user.region) {
      const region = await this.regionRepository.findOne({
        where: { id: user.region.id },
      });

      if (region) {
        region.state = savedState;
        region.status = RegionStatus.STATE_MEMBER;
        await this.regionRepository.save(region);
      }
    }

    return this.findById(savedState.id);
  }

  /**
   * Create a temporary state for revolution with open borders
   */
  async createRevolutionState(
    stateName: string,
    regionId: string,
  ): Promise<State> {
    // Create the state with open borders
    const newState = this.stateRepository.create({
      name: stateName,
      description: 'A temporary state created after a successful revolution',
      isActive: true,
      hasOpenBorders: true, // Enable open borders for revolution states
      treasury: 0,
      resourceReserves: {},
      allies: [],
      enemies: [],
      governmentType: GovernmentType.REPUBLIC, // Revolution states start as republics
    });

    // Save the state first to get an ID
    const savedState = await this.stateRepository.save(newState);

    return savedState;
  }

  async update(
    id: string,
    updateStateDto: UpdateStateDto,
    userId: number,
  ): Promise<State> {
    const state = await this.findById(id);

    if (state.leader.id !== userId) {
      throw new BadRequestException('Only the leader can update the state');
    }

    // Update allowed properties
    if (updateStateDto.name) {
      state.name = updateStateDto.name;
    }

    if (updateStateDto.description) {
      state.description = updateStateDto.description;
    }

    if (updateStateDto.flagUrl) {
      state.flagUrl = updateStateDto.flagUrl;
    }

    if (updateStateDto.isActive !== undefined) {
      state.isActive = updateStateDto.isActive;
    }

    if(updateStateDto.hasOpenBorders){
      state.hasOpenBorders = updateStateDto.hasOpenBorders;
    }

    // Handle government type change
    if (updateStateDto.governmentType !== undefined) {
      const oldGovernmentType = state.governmentType;
      state.governmentType = updateStateDto.governmentType;

      // If changing to republic, trigger elections
      if (updateStateDto.governmentType === GovernmentType.REPUBLIC && oldGovernmentType !== GovernmentType.REPUBLIC) {
        await this.stateRepository.save(state);
        try {
          await this.stateElectionService.triggerElectionsForNewRepublic(id);
        } catch (error) {
          console.error(`Failed to trigger elections for new republic: ${error.message}`);
        }
        return this.findById(id);
      }
    }

    await this.stateRepository.save(state);
    return this.findById(id);
  }

  async addRegion(stateId: string, addRegionDto: AddRegionDto): Promise<State> {
    const state = await this.findById(stateId);
    const region = await this.regionRepository.findOne({
      where: { id: addRegionDto.regionId },
      relations: ['state'],
    });

    if (!region) {
      throw new NotFoundException(
        `Region with ID ${addRegionDto.regionId} not found`,
      );
    }

    // Add the region to the state
    region.state = state;
    region.status = RegionStatus.STATE_MEMBER;
    await this.regionRepository.save(region);

    return this.findById(stateId);
  }

  async removeRegion(stateId: string, regionId: string): Promise<State> {
    // Verify the state exists
    await this.findById(stateId);

    const region = await this.regionRepository.findOne({
      where: { id: regionId },
      relations: ['state'],
    });

    if (!region) {
      throw new NotFoundException(`Region with ID ${regionId} not found`);
    }

    // Check if region is part of this state
    if (!region.state || region.state.id !== stateId) {
      throw new BadRequestException('Region is not part of this state');
    }

    // Remove the region from the state
    region.state = {} as State; // Empty object that satisfies the type
    region.status = RegionStatus.INDEPENDENT;
    await this.regionRepository.save(region);

    return this.findById(stateId);
  }

  async changeLeader(stateId: string, newLeaderId: number): Promise<State> {
    const state = await this.findById(stateId);
    const newLeader = await this.userRepository.findOne({
      where: { id: newLeaderId },
    });

    if (!newLeader) {
      throw new NotFoundException(`User with ID ${newLeaderId} not found`);
    }

    state.leader = newLeader;
    await this.stateRepository.save(state);

    return this.findById(stateId);
  }

  async addAlly(stateId: string, allyStateId: string): Promise<State> {
    const state = await this.findById(stateId);
    const allyState = await this.findById(allyStateId);

    // Check if already allies
    if (state.allies && state.allies.includes(allyStateId)) {
      throw new BadRequestException('States are already allies');
    }

    // Add to allies list
    if (!state.allies) {
      state.allies = [];
    }
    state.allies.push(allyStateId);

    // Mutual alliance - add to ally's allies list too
    if (!allyState.allies) {
      allyState.allies = [];
    }
    allyState.allies.push(stateId);

    // Remove from enemies if applicable
    if (state.enemies && state.enemies.includes(allyStateId)) {
      state.enemies = state.enemies.filter((id) => id !== allyStateId);
    }

    if (allyState.enemies && allyState.enemies.includes(stateId)) {
      allyState.enemies = allyState.enemies.filter((id) => id !== stateId);
    }

    await this.stateRepository.save(state);
    await this.stateRepository.save(allyState);

    return this.findById(stateId);
  }

  async addEnemy(stateId: string, enemyStateId: string): Promise<State> {
    const state = await this.findById(stateId);
    const enemyState = await this.findById(enemyStateId);

    // Check if already enemies
    if (state.enemies && state.enemies.includes(enemyStateId)) {
      throw new BadRequestException('States are already enemies');
    }

    // Add to enemies list
    if (!state.enemies) {
      state.enemies = [];
    }
    state.enemies.push(enemyStateId);

    // Mutual enmity - add to enemy's enemies list too
    if (!enemyState.enemies) {
      enemyState.enemies = [];
    }
    enemyState.enemies.push(stateId);

    // Remove from allies if applicable
    if (state.allies && state.allies.includes(enemyStateId)) {
      state.allies = state.allies.filter((id) => id !== enemyStateId);
    }

    if (enemyState.allies && enemyState.allies.includes(stateId)) {
      enemyState.allies = enemyState.allies.filter((id) => id !== stateId);
    }

    await this.stateRepository.save(state);
    await this.stateRepository.save(enemyState);

    return this.findById(stateId);
  }

  async getStateResources(stateId: string): Promise<any> {
    const state = await this.findById(stateId);

    // Get all regions' resources
    const regionResources = await Promise.all(
      state.regions.map(async (region) => {
        const fullRegion = await this.regionRepository.findOne({
          where: { id: region.id },
        });
        return fullRegion ? fullRegion.resources || {} : {};
      }),
    );

    // Combine region resources with state reserves
    const totalResources = {
      gold: { current: state.resourceReserves.gold || 0, max: 0 },
      oil: { current: state.resourceReserves.oil || 0, max: 0 },
      ore: { current: state.resourceReserves.ore || 0, max: 0 },
      uranium: { current: state.resourceReserves.uranium || 0, max: 0 },
      diamonds: { current: state.resourceReserves.diamonds || 0, max: 0 },
    };

    regionResources.forEach((resources) => {
      if (resources.gold) {
        totalResources.gold.current += resources.gold.current || 0;
        totalResources.gold.max += resources.gold.max || 0;
      }
      if (resources.oil) {
        totalResources.oil.current += resources.oil.current || 0;
        totalResources.oil.max += resources.oil.max || 0;
      }
      if (resources.ore) {
        totalResources.ore.current += resources.ore.current || 0;
        totalResources.ore.max += resources.ore.max || 0;
      }
      if (resources.uranium) {
        totalResources.uranium.current += resources.uranium.current || 0;
        totalResources.uranium.max += resources.uranium.max || 0;
      }
      if (resources.diamonds) {
        totalResources.diamonds.current += resources.diamonds.current || 0;
        totalResources.diamonds.max += resources.diamonds.max || 0;
      }
    });

    return {
      treasury: state.treasury,
      resourceReserves: state.resourceReserves,
      totalResources,
    };
  }

  async removeState(id: string): Promise<DeleteResult> {
    // Find the state to get its leader for notifications
    const state = await this.findById(id);

    if (!state) {
      throw new NotFoundException(`State with ID ${id} not found`);
    }

    // Get all regions belonging to this state
    const regions = await this.regionRepository.find({
      where: { state: { id } },
    });

    // Set all regions to independent
    for (const region of regions) {
      region.state = null;
      region.status = RegionStatus.INDEPENDENT;
      await this.regionRepository.save(region);
    }

    // Delete the state
    return await this.stateRepository.delete(id);
  }

  /**
   * Change government type of a state
   * @param stateId The ID of the state
   * @param governmentType The new government type
   * @param userId The ID of the user making the change (must be state leader for manual changes)
   * @param isRevolutionVictory Whether this change is due to a revolution victory (bypasses leader check and active election check)
   */
  async changeGovernmentType(
    stateId: string,
    governmentType: GovernmentType,
    userId?: number,
    isRevolutionVictory: boolean = false
  ): Promise<State> {
    const state = await this.findById(stateId);

    // If not a revolution victory, verify the user is the state leader
    if (!isRevolutionVictory) {
      if (!userId) {
        throw new BadRequestException('User ID is required for manual government changes');
      }

      if (state.leader?.id !== userId) {
        throw new BadRequestException('Only the state leader can change the government type');
      }

      // Check if there are active elections - government type cannot be changed during elections
      try {
        const activeElection = await this.stateElectionService.getActiveElectionForState(stateId);
        if (activeElection) {
          throw new BadRequestException('Government type cannot be changed while elections are active. Please wait for the current election to finish.');
        }
      } catch (error) {
        // If the error is our BadRequestException, re-throw it
        if (error instanceof BadRequestException) {
          throw error;
        }
        // For other errors (like service not available), log and continue
        console.warn(`Could not check for active elections: ${error.message}`);
      }
    }

    // Update the government type
    state.governmentType = governmentType;
    await this.stateRepository.save(state);

    // If changing to republic, trigger elections
    if (governmentType === GovernmentType.REPUBLIC) {
      try {
        await this.stateElectionService.triggerElectionsForNewRepublic(stateId);
      } catch (error) {
        console.error(`Failed to trigger elections for new republic: ${error.message}`);
        // Continue even if elections fail - the government type has been changed
      }
    }

    return this.findById(stateId);
  }

  /**
   * Change government to republic after a revolution victory
   */
  async changeToRepublicAfterRevolution(stateId: string): Promise<State> {
    return this.changeGovernmentType(stateId, GovernmentType.REPUBLIC, undefined, true);
  }
}
